import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { whopIntermediaryClient } from '@/lib/whopIntermediaryClient';

// The specific product ID we're checking for
const REQUIRED_PRODUCT_ID = 'plan_WndUmy2gBlCAc';

// localStorage key for persisting payment state
const WHOP_PAYMENT_STORAGE_KEY = 'whop_payment_state';

// Utility functions for localStorage persistence
const savePaymentStateToStorage = (hasPaid: boolean, receiptId?: string) => {
  if (typeof window === 'undefined') return;

  try {
    const paymentState = {
      hasPaid,
      receiptId,
      timestamp: new Date().toISOString()
    };
    localStorage.setItem(WHOP_PAYMENT_STORAGE_KEY, JSON.stringify(paymentState));
    console.log('💾 Saved payment state to localStorage:', paymentState);
  } catch (error) {
    console.error('❌ Failed to save payment state to localStorage:', error);
  }
};

const loadPaymentStateFromStorage = () => {
  if (typeof window === 'undefined') return { hasPaid: false, receiptId: null };

  try {
    const stored = localStorage.getItem(WHOP_PAYMENT_STORAGE_KEY);
    if (stored) {
      const paymentState = JSON.parse(stored);
      console.log('📂 Loaded payment state from localStorage:', paymentState);
      return {
        hasPaid: paymentState.hasPaid || false,
        receiptId: paymentState.receiptId || null
      };
    }
  } catch (error) {
    console.error('❌ Failed to load payment state from localStorage:', error);
  }

  return { hasPaid: false, receiptId: null };
};

const clearPaymentStateFromStorage = () => {
  if (typeof window === 'undefined') return;

  try {
    localStorage.removeItem(WHOP_PAYMENT_STORAGE_KEY);
    console.log('🗑️ Cleared payment state from localStorage');
  } catch (error) {
    console.error('❌ Failed to clear payment state from localStorage:', error);
  }
};

interface WhopPaymentContextType {
  // State
  hasPaid: boolean;
  receiptId: string | null;
  isLoading: boolean;
  error: string | null;
  showPaymentModal: boolean;
  hasProductAccess: boolean;

  // Actions
  setPaymentSuccess: (receiptId: string) => void;
  setPaymentError: (error: string) => void;
  clearPaymentState: () => void;
  checkPaymentStatus: () => Promise<void>;
  checkProductAccess: () => Promise<void>;
  setShowPaymentModal: (show: boolean) => void;
}

const WhopPaymentContext = createContext<WhopPaymentContextType | undefined>(undefined);

interface WhopPaymentProviderProps {
  children: ReactNode;
}

export const WhopPaymentProvider: React.FC<WhopPaymentProviderProps> = ({ children }) => {
  // Initialize state from localStorage if available
  const storedPayment = loadPaymentStateFromStorage();
  const [hasPaid, setHasPaid] = useState(storedPayment.hasPaid);
  const [receiptId, setReceiptId] = useState<string | null>(storedPayment.receiptId);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [hasProductAccess, setHasProductAccess] = useState(false);

  const setPaymentSuccess = (newReceiptId: string) => {
    setHasPaid(true);
    setReceiptId(newReceiptId);
    setError(null);
    setShowPaymentModal(false);

    // Save to localStorage
    savePaymentStateToStorage(true, newReceiptId);

    console.log('✅ Payment success recorded:', { receiptId: newReceiptId });
  };

  const setPaymentError = (errorMessage: string) => {
    setError(errorMessage);
    console.error('❌ Payment error recorded:', errorMessage);
  };

  const clearPaymentState = () => {
    setHasPaid(false);
    setReceiptId(null);
    setError(null);
    setShowPaymentModal(false);

    // Clear from localStorage
    clearPaymentStateFromStorage();

    console.log('🗑️ Payment state cleared');
  };

  const setShowPaymentModalState = (show: boolean) => {
    setShowPaymentModal(show);
  };

  const checkPaymentStatus = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Here you could implement a server-side check to verify payment status
      // For now, we rely on localStorage persistence
      console.log('🔍 Checking payment status...');

      // If we have a stored payment state, trust it
      const storedPayment = loadPaymentStateFromStorage();
      if (storedPayment.hasPaid && storedPayment.receiptId) {
        setHasPaid(true);
        setReceiptId(storedPayment.receiptId);
        setShowPaymentModal(false);
        console.log('✅ Found valid payment state in storage');
      } else {
        // No payment found
        console.log('⚠️ No payment found in storage');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to check payment status';
      setError(errorMessage);
      console.error('❌ Error checking payment status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Check if user has access to the required product
  const checkProductAccess = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log(`🔍 Checking access to product: ${REQUIRED_PRODUCT_ID}`);

      const client = whopIntermediaryClient as any;
      const response = await client.checkUserAccessToProduct(REQUIRED_PRODUCT_ID);

      if (response.success) {
        const hasAccess = response.data.hasAccess;
        setHasProductAccess(hasAccess);

        // If user has access, mark as paid and hide payment modal
        if (hasAccess) {
          setHasPaid(true);
          setShowPaymentModal(false);
          savePaymentStateToStorage(true, `product_access_${REQUIRED_PRODUCT_ID}`);
        } else {
          // If user doesn't have access, show payment modal
          setHasPaid(false);
          setShowPaymentModal(true);
        }

        console.log('✅ Product access checked:', { hasAccess, productId: REQUIRED_PRODUCT_ID });
      } else {
        throw new Error(response.error || 'Failed to check product access');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to check product access';
      setError(errorMessage);
      console.error('❌ Error checking product access:', err);

      // On error, assume no access and show payment modal
      setHasProductAccess(false);
      setShowPaymentModal(true);
    } finally {
      setIsLoading(false);
    }
  };

  // Log state changes in development
  useEffect(() => {
    if (import.meta.env.DEV) {
      console.log('🔧 Whop Payment Context State:', {
        hasPaid,
        receiptId: receiptId ? `${receiptId.substring(0, 10)}...` : null,
        isLoading,
        error,
        showPaymentModal
      });
    }
  }, [hasPaid, receiptId, isLoading, error, showPaymentModal]);

  const value: WhopPaymentContextType = {
    hasPaid,
    receiptId,
    isLoading,
    error,
    showPaymentModal,
    hasProductAccess,
    setPaymentSuccess,
    setPaymentError,
    clearPaymentState,
    checkPaymentStatus,
    checkProductAccess,
    setShowPaymentModal: setShowPaymentModalState
  };

  return (
    <WhopPaymentContext.Provider value={value}>
      {children}
    </WhopPaymentContext.Provider>
  );
};

export const useWhopPayment = (): WhopPaymentContextType => {
  const context = useContext(WhopPaymentContext);
  if (context === undefined) {
    throw new Error('useWhopPayment must be used within a WhopPaymentProvider');
  }
  return context;
};

// Helper function for checking if user has access (paid or not a Whop user)
// This is now handled directly in components to avoid hook dependency issues

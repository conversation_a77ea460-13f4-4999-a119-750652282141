/**
 * Official Whop iframe SDK integration
 * Uses the official @whop/iframe package
 */

import { createSdk } from "@whop/iframe";

// Get the appropriate app ID based on current path
const getAppId = () => {
  if (typeof window === 'undefined') return import.meta.env.VITE_WHOP_APP_ID;

  const path = window.location.pathname;
  if (path.includes('/trade')) {
    return import.meta.env.VITE_TRADING_WHOP_APP_ID || import.meta.env.VITE_WHOP_APP_ID;
  }
  return import.meta.env.VITE_WHOP_APP_ID;
};

// Create the official iframe SDK instance
export const iframeSdk = createSdk({
  appId: getAppId(),
});

console.log('🔧 Official Whop iframe SDK initialized:', {
  appId: getAppId(),
  timestamp: new Date().toISOString(),
  windowContext: typeof window !== 'undefined' ? {
    isInIframe: window.parent !== window,
    origin: window.location.origin,
    pathname: window.location.pathname,
    referrer: document.referrer
  } : 'SSR'
});

// Check if SDK is properly initialized
console.log('🔧 Whop iframe SDK status:', {
  sdkExists: !!iframeSdk,
  hasInAppPurchase: !!(iframeSdk as any)?.inAppPurchase,
  hasOpenExternalUrl: !!(iframeSdk as any)?.openExternalUrl,
  sdkMethods: iframeSdk ? Object.keys(iframeSdk) : []
});

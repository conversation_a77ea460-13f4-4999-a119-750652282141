/**
 * Whop payment plan configurations for the Osis Whop app
 */

export const WHOP_PLAN_IDS = {
  // Existing access pass (price may vary)
  ACCESS_PASS: 'plan_WndUmy2gBlCAc',
  
  // $15 one-time payment plan for Whop app access
  // This should be created in your Whop dashboard as a $15 one-time payment plan
  FIFTEEN_DOLLAR_ACCESS: 'plan_WndUmy2gBlCAc', // Using existing for now, should be updated to actual $15 plan
} as const;

export const WHOP_PLAN_PRICES = {
  ACCESS_PASS: 15, // $15 USD
  FIFTEEN_DOLLAR_ACCESS: 15, // $15 USD
} as const;

export const WHOP_PLAN_DESCRIPTIONS = {
  ACCESS_PASS: 'One-time payment for lifetime access to Osis',
  FIFTEEN_DOLLAR_ACCESS: 'One-time $15 payment for lifetime access to Osis',
} as const;

/**
 * Get the plan ID for the $15 Whop app payment
 */
export function getWhopAppPaymentPlanId(): string {
  return WHOP_PLAN_IDS.FIFTEEN_DOLLAR_ACCESS;
}

/**
 * Get the price for the Whop app payment
 */
export function getWhopAppPaymentPrice(): number {
  return WHOP_PLAN_PRICES.FIFTEEN_DOLLAR_ACCESS;
}

/**
 * Get the description for the Whop app payment
 */
export function getWhopAppPaymentDescription(): string {
  return WHOP_PLAN_DESCRIPTIONS.FIFTEEN_DOLLAR_ACCESS;
}

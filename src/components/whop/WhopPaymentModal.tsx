import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { CreditCard, Loader2, CheckCircle, AlertCircle, Crown } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
// Try importing from the specific file path to avoid any resolution issues
import { whopIntermediaryClient as mainClient } from '@/lib/whopIntermediaryClient';
import { whopIntermediaryClient as sharedClient } from '@/shared/src/lib/whopIntermediaryClient';
import { iframeSdk } from '@/lib/iframe-sdk';

// Cast to any to bypass TypeScript issues
const mainClientAny = mainClient as any;
const sharedClientAny = sharedClient as any;

// Debug: Check both clients
console.log('🔍 Main client methods:', mainClientAny ? Object.keys(mainClientAny) : 'undefined');
console.log('🔍 Shared client methods:', sharedClientAny ? Object.keys(sharedClientAny) : 'undefined');

// Use the client that has the createCheckoutSession method (not createCharge)
const client = (mainClientAny && mainClientAny.createCheckoutSession) ? mainClientAny :
               (sharedClientAny && sharedClientAny.createCheckoutSession) ? sharedClientAny :
               mainClientAny; // fallback to main client
import { useToast } from '@/hooks/use-toast';

// Note: We use the global whopIframeSdk instead of the React hook to avoid hook dependency issues

interface WhopPaymentModalProps {
  isOpen: boolean;
  onPaymentSuccess: () => void;
  onPaymentError?: (error: string) => void;
}

const WhopPaymentModal: React.FC<WhopPaymentModalProps> = ({
  isOpen,
  onPaymentSuccess,
  onPaymentError
}) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Payment configuration
  const PAYMENT_AMOUNT = 44; // $44.00
  const PAYMENT_DESCRIPTION = 'Osis AI Trading Platform - Monthly Access';
  const REQUIRED_PRODUCT_ID = 'prod_uQ4Wo1ZKs6zYD';

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setError(null);
      setSuccess(false);
      setIsLoading(false);
    }
  }, [isOpen]);

  const handlePurchase = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log(`💳 Starting Osis Whop app payment ($${PAYMENT_AMOUNT})...`);

      // Cast to any for runtime access
      const clientAny = client as any;

      // Debug: Check if whopIntermediaryClient is available
      console.log('🔍 Checking whopIntermediaryClient:', {
        exists: !!clientAny,
        hasCreateCheckoutSession: !!(clientAny && clientAny.createCheckoutSession),
        hasCreateCharge: !!(clientAny && clientAny.createCharge),
        type: typeof clientAny,
        methods: clientAny ? Object.keys(clientAny) : [],
        allMethods: clientAny ? Object.getOwnPropertyNames(clientAny) : [],
        prototype: clientAny ? Object.getPrototypeOf(clientAny) : null
      });

      // Log the actual client object for debugging
      console.log('🔍 Full client object:', clientAny);

      if (!clientAny) {
        throw new Error('Whop intermediary client is not available');
      }

      if (!clientAny.createCheckoutSession) {
        console.error('❌ Available methods:', Object.keys(clientAny));
        throw new Error(`createCheckoutSession method is not available on whopIntermediaryClient. Available methods: ${Object.keys(clientAny).join(', ')}`);
      }

      // Get experience ID for tracking (if available)
      const experienceId = new URLSearchParams(window.location.search).get('experienceId') ||
                          window.location.pathname.split('/experiences/')[1];

      // Step 1: Create checkout session for the specific product
      console.log('🛒 Creating checkout session for product:', REQUIRED_PRODUCT_ID);
      const checkoutResponse = await clientAny.createCheckoutSession(
        REQUIRED_PRODUCT_ID,
        {
          experienceId,
          source: 'trade_sensei_app',
          product: 'lifetime_access',
          description: PAYMENT_DESCRIPTION
        }
      );

      console.log('📡 Checkout session response:', checkoutResponse);

      if (!checkoutResponse.success || !checkoutResponse.data?.id) {
        throw new Error(checkoutResponse.error || 'Failed to create checkout session');
      }

      // Step 2: Use the properly imported iframe SDK
      console.log('🔍 Checking iframe SDK:', {
        exists: !!iframeSdk,
        hasInAppPurchase: !!(iframeSdk && (iframeSdk as any).inAppPurchase),
        type: typeof iframeSdk,
        methods: iframeSdk ? Object.keys(iframeSdk) : [],
        fullObject: iframeSdk
      });

      // Also check if there's a global whopIframeSdk as fallback
      const globalWhopSdk = (window as any).whopIframeSdk;
      console.log('🔍 Checking global whopIframeSdk:', {
        exists: !!globalWhopSdk,
        hasInAppPurchase: !!(globalWhopSdk && globalWhopSdk.inAppPurchase),
        type: typeof globalWhopSdk,
        methods: globalWhopSdk ? Object.keys(globalWhopSdk) : []
      });

      // Use the imported SDK first, fallback to global if needed
      const whopSdk = iframeSdk || globalWhopSdk;

      if (!whopSdk) {
        throw new Error('Whop iframe SDK not available. Please check your Whop app configuration.');
      }

      const whopSdkAny = whopSdk as any;
      if (!whopSdkAny.inAppPurchase) {
        console.error('❌ Available methods on SDK:', Object.keys(whopSdkAny));
        throw new Error(`inAppPurchase method not available on iframe SDK. Available methods: ${Object.keys(whopSdkAny).join(', ')}`);
      }

      // Step 3: Open Whop payment modal with the checkout session
      console.log('🖼️ Opening Whop payment modal...');
      const paymentResult = await whopSdkAny.inAppPurchase(checkoutResponse.data);

      console.log('💳 Payment result:', paymentResult);

      if (paymentResult?.status === "ok") {
        setSuccess(true);
        console.log('✅ Payment successful!', paymentResult.data);

        // Show success toast
        toast({
          title: "Welcome to Osis!",
          description: "Your payment was successful. You now have monthly access to all features!",
        });

        // Call success callback after a short delay to show success state
        setTimeout(() => {
          onPaymentSuccess();
        }, 2000);

      } else {
        throw new Error(paymentResult?.error || 'Payment was not completed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Payment failed. Please try again.';
      setError(errorMessage);
      console.error('❌ Payment error:', error);

      if (onPaymentError) {
        onPaymentError(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleRetry = () => {
    setError(null);
    handlePurchase();
  };

  // Success state
  if (success) {
    return (
      <Dialog open={isOpen} onOpenChange={() => {}}>
        <DialogContent className="bg-[#0A0A0A] border-green-500/20 text-white max-w-md [&>button]:hidden">
          <div className="text-center py-8">
            <div className="flex flex-col items-center space-y-6">
              <div className="relative">
                <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-8 w-8 text-green-400" />
                </div>
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <Crown className="h-3 w-3 text-white" />
                </div>
              </div>
              
              <div className="space-y-2">
                <h2 className="text-xl font-bold text-green-400">Payment Successful!</h2>
                <p className="text-white/70 text-sm">
                  Welcome to Osis! You now have full access to all features.
                </p>
              </div>

              <div className="w-full bg-green-500/10 border border-green-500/20 rounded-lg p-3">
                <p className="text-green-300 text-xs text-center">
                  🎉 Unlocking your trading experience...
                </p>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="bg-[#0A0A0A] border-white/[0.08] text-white max-w-md [&>button]:hidden">
        <DialogHeader className="text-center pb-2">
          <div className="flex justify-center mb-4">
            <div className="relative">
              <div className="w-20 h-20 bg-white/5 rounded-2xl flex items-center justify-center border border-white/10 shadow-inner">
                <img
                  src="http://thecodingkid.oyosite.com/logo_only.png"
                  alt="Osis Logo"
                  className="w-12 h-12 object-contain"
                />
              </div>
              <div className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-lg">
                <Crown className="h-3 w-3 text-white" />
              </div>
            </div>
          </div>

          <DialogTitle className="text-2xl font-bold text-white">
            Unlock Osis
          </DialogTitle>
          <DialogDescription className="text-white/60 text-base mt-2">
            Get instant access to the complete AI trading platform
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Features List */}
          <div className="space-y-3">
            <h3 className="text-sm font-semibold text-white/80 mb-3">What you'll get:</h3>
            {[
              'AI-powered trading agents',
              'Advanced stock scanner',
              'Real-time market analysis',
              'Custom agent builder',
              'Portfolio management',
              'Community marketplace'
            ].map((feature, index) => (
              <div key={index} className="flex items-center gap-3">
                <div className="w-5 h-5 bg-green-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                  <CheckCircle className="h-3 w-3 text-green-400" />
                </div>
                <span className="text-white/70 text-sm">{feature}</span>
              </div>
            ))}
          </div>

          {/* Pricing */}
          <Card className="bg-gradient-to-br from-white/5 to-white/10 border-white/10 shadow-inner">
            <CardContent className="p-4 text-center">
              <div className="space-y-2">
                <div className="flex items-center justify-center gap-2">
                  <span className="text-3xl font-bold text-white">${PAYMENT_AMOUNT}</span>
                  <Badge className="bg-green-500/20 text-green-300 border-green-500/30">
                    Monthly
                  </Badge>
                </div>
                <p className="text-white/60 text-xs">
                  Full access • Cancel anytime
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Error Display */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
              <div className="flex items-center gap-2 text-red-400 text-sm">
                <AlertCircle className="h-4 w-4 flex-shrink-0" />
                <span>{error}</span>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button
              onClick={handlePurchase}
              disabled={isLoading}
              className="w-full bg-white text-black hover:bg-white/90 py-3 text-base font-semibold transition-all duration-200 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.2)] hover:shadow-[inset_0_1px_0_0_rgba(255,255,255,0.3)] border-0"
              style={{
                boxShadow: 'inset 0 1px 0 0 rgba(255, 255, 255, 0.2), 0 4px 12px rgba(0, 0, 0, 0.15)'
              }}
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Processing Payment...</span>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4" />
                  <span>Pay ${PAYMENT_AMOUNT} & Get Access</span>
                </div>
              )}
            </Button>

            {error && (
              <Button
                onClick={handleRetry}
                variant="outline"
                className="w-full border-white/[0.12] text-white/70 hover:text-white hover:bg-white/[0.04]"
                disabled={isLoading}
              >
                Try Again
              </Button>
            )}
          </div>

          {/* Security Notice */}
          <div className="text-center">
            <p className="text-xs text-white/50">
              🔒 Secure payment processed by Whop
              <br />
              Your payment information is protected
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default WhopPaymentModal;

/* TradingChart.css */
.trading-chart-container {
  width: 100%;
  height: 100%;
  min-height: 600px; /* Ensure minimum height for proper chart rendering */
  background-color: #0A0A0A;
  color: #d1d4dc;
  border-radius: 12px;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  position: relative;
  display: flex;
  flex-direction: column;
  isolation: isolate; /* Prevent sidebar from affecting chart positioning */
}

/* Navigator left section with search and timeframes */
.toolbar-left {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
}

/* Clean scanner search */
.futuristic-scanner-search {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, rgba(20, 20, 20, 0.95) 0%, rgba(15, 15, 15, 0.9) 100%);
  border: 1px solid rgba(0, 231, 182, 0.3);
  border-radius: 8px;
  padding: 6px 10px;
  backdrop-filter: blur(20px);
  box-shadow:
    0 0 15px rgba(0, 231, 182, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
  height: 32px;
  margin-left: 0px;
  width: 280px;
  max-width: calc(100vw - 60px);
  position: relative;
  flex-shrink: 0;
}

.futuristic-scanner-search::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 231, 182, 0.3), transparent);
  animation: scanner-sweep 3s infinite;
}

@keyframes scanner-sweep {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

.scanner-input-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.scanner-input {
  background: transparent;
  border: none;
  color: #ffffff;
  font-size: 12px;
  font-weight: 500;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.01em;
  text-transform: uppercase;
  outline: none;
  flex: 1;
  padding: 6px 8px;
  height: 20px;
}

.scanner-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
  font-weight: 400;
  letter-spacing: -0.01em;
  text-transform: none;
}

/* Green circular loading indicator */
.scanner-loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.loading-circle {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(0, 231, 182, 0.2);
  border-top: 2px solid #00e7b6;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
  box-shadow: 0 0 8px rgba(0, 231, 182, 0.4);
}

@keyframes loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.scanner-status {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 3px 4px;
  background: rgba(0, 231, 182, 0.08);
  border: 1px solid rgba(0, 231, 182, 0.2);
  border-radius: 4px;
  backdrop-filter: blur(10px);
  white-space: nowrap;
  flex-shrink: 0;
}

.status-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #00e7b6;
  box-shadow: 0 0 8px rgba(0, 231, 182, 0.8);
  animation: status-pulse 1.5s infinite;
}

.status-indicator.active {
  /* Ensure active state doesn't have conflicting animations */
  animation: status-pulse 1.5s infinite;
}

@keyframes status-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.2);
  }
}

@keyframes scanning-progress {
  0% {
    width: 20%;
    transform: translateX(-100%);
  }
  50% {
    width: 80%;
    transform: translateX(0%);
  }
  100% {
    width: 60%;
    transform: translateX(50%);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::before {
  width: 300px;
  height: 300px;
}

.status-text {
  font-size: 9px;
  font-weight: 700;
  color: #00e7b6;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;
  text-shadow: 0 0 6px rgba(0, 231, 182, 0.4);
}

.search-input {
  width: 100%;
  padding: 8px 35px 8px 45px;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4));
  border: 1px solid rgba(0, 231, 182, 0.3);
  border-radius: 20px;
  color: #ffffff;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0.3px;
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.3),
              0 0 15px rgba(0, 231, 182, 0.15);
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: rgba(0, 231, 182, 0.7);
  box-shadow: 0 0 25px rgba(0, 231, 182, 0.25),
              inset 0 2px 5px rgba(0, 0, 0, 0.3);
  background: linear-gradient(to right, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5));
}

.search-input::placeholder {
  color: rgba(0, 231, 182, 0.8);
  opacity: 0.9;
  font-weight: 500;
}

.seamless-search-input {
  width: 100%;
  height: 48px;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  padding: 0 40px 0 0;
  outline: none;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 500;
  letter-spacing: 0.3px;
}

.seamless-search-input:focus {
  color: #ffffff;
}

.seamless-search-input::placeholder {
  color: rgba(255, 255, 255, 0.3);
  font-size: 13px;
  font-weight: 400;
  letter-spacing: 0.2px;
}

.search-spinner {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(0, 231, 182, 0.8);
  font-size: 16px;
  animation: spin 1s linear infinite;
  filter: drop-shadow(0 0 8px rgba(0, 231, 182, 0.5));
}

.aura-logo {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.loading-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(0, 231, 182, 0.9);
  font-size: 16px;
  animation: spin 1s linear infinite;
  filter: drop-shadow(0 0 8px rgba(0, 231, 182, 0.6));
  z-index: 3;
}

@keyframes spin {
  0% { transform: translateY(-50%) rotate(0deg); }
  100% { transform: translateY(-50%) rotate(360deg); }
}

.error-message {
  padding: 10px 16px;
  background-color: rgba(255, 82, 82, 0.08);
  color: #ff5252;
  font-size: 14px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 82, 82, 0.15);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
  font-weight: 500;
}

/* Chart Navigator */
.chart-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
  height: 52px;
  background: linear-gradient(180deg, rgba(15, 15, 15, 0.98) 0%, rgba(10, 10, 10, 0.95) 100%);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.03);
  position: relative;
  z-index: 10;
}

/* Timeframe selector styling */
.timeframe-selector {
  display: flex;
  gap: 8px;
}

.timeframe-selector button {
  font-size: 12px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.6);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.01em;
  background: rgba(20, 20, 20, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 4px 10px;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.03);
}

.timeframe-selector button:hover {
  background-color: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  border-color: rgba(255, 255, 255, 0.15);
}

.timeframe-selector button.active {
  background-color: rgba(0, 231, 182, 0.15);
  color: #00e7b6;
  border-color: rgba(0, 231, 182, 0.4);
  box-shadow: 0 0 15px rgba(0, 231, 182, 0.2), inset 0 1px 0 rgba(0, 231, 182, 0.1);
  text-shadow: 0 0 10px rgba(0, 231, 182, 0.4);
}

/* Chart controls and filters - positioned in corner */
.chart-controls {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  align-items: center;
  background: rgba(10, 10, 10, 0.7);
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 8px 18px;
  gap: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chart-button {
  background: none;
  border: none;
  color: rgba(255,255,255,0.75);
  padding: 8px 10px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: color 0.2s;
  font-weight: 500;
  min-width: 44px;
  height: 40px;
  gap: 4px;
  position: relative;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
}

.chart-button:hover, .chart-button.active {
  color: #00e7b6;
  background: none;
}

.chart-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, rgba(0, 231, 182, 0), rgba(0, 231, 182, 0.3), rgba(0, 231, 182, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chart-button svg {
  font-size: 13px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.4));
}

/* TradingView-style icon buttons */
.chart-button-icon {
  width: 16px;
  height: 16px;
  opacity: 0.8;
  transition: all 0.2s ease;
  filter: brightness(0) saturate(100%) invert(80%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
  object-fit: contain;
  flex-shrink: 0;
}

.chart-button:hover .chart-button-icon {
  opacity: 1;
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
}

.chart-button.active .chart-button-icon {
  opacity: 1;
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
}

.chart-button-text {
  font-size: 10px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.6);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;
  transition: color 0.2s ease;
  text-align: center;
  line-height: 1;
  white-space: nowrap;
}

.chart-button:hover .chart-button-text {
  color: rgba(255, 255, 255, 0.8);
}

.chart-button.active .chart-button-text {
  color: rgba(255, 255, 255, 0.9);
}

/* Filters dropdown styling */
.filters-dropdown {
  position: relative;
  display: inline-block;
}

.filters-button {
  background: linear-gradient(145deg, rgba(20, 20, 20, 0.95) 0%, rgba(10, 10, 10, 0.9) 100%);
  border: 1px solid rgba(0, 231, 182, 0.25);
  color: #00e7b6;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 7px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.08),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.4),
    0 1px 2px rgba(0, 0, 0, 0.2),
    0 0 15px rgba(0, 231, 182, 0.05);
  letter-spacing: -0.01em;
  height: 32px;
  position: relative;
  overflow: hidden;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  backdrop-filter: blur(8px);
}

.filters-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, rgba(0, 231, 182, 0), rgba(0, 231, 182, 0.3), rgba(0, 231, 182, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.filters-button:hover::before {
  opacity: 1;
}

.filters-button svg {
  font-size: 14px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.4)) drop-shadow(0 0 8px rgba(0, 231, 182, 0.2));
  transition: all 0.3s ease;
}

.filters-button:hover svg {
  filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.5)) drop-shadow(0 0 15px rgba(0, 231, 182, 0.4));
}

.filters-content {
  display: none;
  position: absolute;
  right: 0;
  top: 100%;
  margin-top: 5px;
  background-color: rgba(0, 0, 0, 0.95);
  min-width: 200px;
  border-radius: 8px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(0, 231, 182, 0.1);
  z-index: 20;
  overflow: hidden;
  padding: 12px;
}

.filters-dropdown:hover .filters-content,
.filters-content:hover {
  display: block;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

.filters-title {
  font-size: 12px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 12px;
  letter-spacing: -0.02em;
  text-transform: uppercase;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 231, 182, 0.1);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
}

.filters-description {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 15px;
  line-height: 1.4;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;
}

.filter-options-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 10px;
  color: #d1d4dc;
  font-size: 12px;
  transition: all 0.2s ease;
  cursor: pointer;
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 231, 182, 0.05);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;
}

.filter-option:hover {
  background-color: rgba(0, 231, 182, 0.05);
  border-color: rgba(0, 231, 182, 0.1);
}

.filter-option.active {
  background-color: rgba(0, 231, 182, 0.08);
  border-color: rgba(0, 231, 182, 0.15);
  box-shadow: 0 0 10px rgba(0, 231, 182, 0.1);
}

.filter-option-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.filter-option-icon {
  color: rgba(0, 231, 182, 0.7);
  font-size: 14px;
  filter: drop-shadow(0 0 2px rgba(0, 231, 182, 0.3));
  transition: all 0.2s ease;
}

.filter-option.active .filter-option-icon {
  color: #00e7b6;
  filter: drop-shadow(0 0 3px rgba(0, 231, 182, 0.5));
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 36px;
  height: 20px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(0, 231, 182, 0.2);
  transition: .3s;
  border-radius: 20px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 14px;
  width: 14px;
  left: 2px;
  bottom: 2px;
  background-color: rgba(255, 255, 255, 0.8);
  transition: .3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: rgba(0, 231, 182, 0.2);
  border-color: rgba(0, 231, 182, 0.4);
}

input:checked + .toggle-slider:before {
  transform: translateX(16px);
  background-color: #00e7b6;
  box-shadow: 0 0 8px rgba(0, 231, 182, 0.5);
}

.chart-button:hover {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(16px) saturate(150%);
  color: #ffffff;
  border-color: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  transform: translateY(-1px);
  box-shadow:
    0 6px 24px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.08);
}

.chart-button.active {
  background: rgba(255, 255, 255, 0.15);
  color: #ffffff;
  border-color: rgba(255, 255, 255, 0.25);
  border-radius: 12px;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.25),
    inset 0 -1px 0 rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.12);
}

.chart-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.chart-button:disabled:hover {
  transform: none;
  background: transparent;
  color: rgba(255, 255, 255, 0.4);
}

.filters-button:hover {
  background: linear-gradient(145deg, rgba(0, 231, 182, 0.15) 0%, rgba(0, 180, 140, 0.1) 100%);
  color: #00f5c4;
  border-color: rgba(0, 231, 182, 0.4);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.12),
    inset 0 -1px 0 rgba(0, 0, 0, 0.4),
    0 4px 16px rgba(0, 231, 182, 0.15),
    0 2px 8px rgba(0, 0, 0, 0.4),
    0 0 25px rgba(0, 231, 182, 0.1);
  transform: translateY(-1px);
}

.filters-button.active {
  background-color: rgba(0, 231, 182, 0.08);
  color: #00e7b6;
  border-color: rgba(0, 231, 182, 0.3);
  box-shadow: 0 0 20px rgba(0, 231, 182, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.filters-button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 15%;
  right: 15%;
  height: 1px;
  background: linear-gradient(90deg, rgba(0, 231, 182, 0), rgba(0, 231, 182, 0.5), rgba(0, 231, 182, 0));
}

.chart-controls-divider {
  width: 1px;
  height: 20px;
  background: linear-gradient(to bottom, rgba(0, 231, 182, 0.01), rgba(0, 231, 182, 0.15), rgba(0, 231, 182, 0.01));
  margin: 0 8px;
  opacity: 0.5;
}

.dropdown-arrow {
  font-size: 8px;
  margin-left: 3px;
  color: rgba(0, 231, 182, 0.7);
}

/* Duplicate chart-controls definition removed - using absolute positioned version above */

/* Chart content area - responsive to container */
.chart-content {
  display: flex;
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 400px;
  background-color: #0A0A0A;
}

/* Delete mode styling */
.chart-container.delete-mode {
  cursor: crosshair !important;
}

.chart-container.delete-mode * {
  cursor: crosshair !important;
}

/* Drawing Tools Dropdown */
.drawing-tools-dropdown-container {
  position: relative;
}

/* Executor Sidebar */
.executor-sidebar-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(8px);
  z-index: 1998;
  opacity: 0;
  animation: fadeIn 0.3s ease-out forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.executor-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: 380px;
  height: 100vh;
  background: rgba(10, 10, 10, 0.85);
  backdrop-filter: blur(40px) saturate(180%);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    -8px 0 32px rgba(0, 0, 0, 0.6),
    inset 1px 0 0 rgba(255, 255, 255, 0.05);
  z-index: 1999;
  transform: translateX(100%);
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  flex-direction: column;
  will-change: transform;
}

.executor-sidebar.open {
  transform: translateX(0);
}

/* Header */
.executor-sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: linear-gradient(180deg, rgba(0, 231, 182, 0.08) 0%, rgba(0, 0, 0, 0.8) 100%);
}

.executor-symbol-badge {
  background: linear-gradient(145deg, rgba(0, 231, 182, 0.15) 0%, rgba(0, 200, 150, 0.08) 100%);
  border: 1px solid rgba(0, 231, 182, 0.3);
  color: #00e7b6;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-shadow: 0 0 8px rgba(0, 231, 182, 0.3);
}

.executor-price-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.executor-current-price {
  font-size: 18px;
  font-weight: 700;
  color: #00e7b6;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;
  text-shadow: 0 0 8px rgba(0, 231, 182, 0.3);
}

.executor-close-button {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.executor-close-button:hover {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.05);
}

/* Content */
.executor-sidebar-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.executor-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.executor-section-title {
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.01em;
  text-transform: uppercase;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

/* Quick Buttons */
.executor-quick-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.executor-buy-button,
.executor-sell-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  width: 100%;
}

.executor-buy-button {
  background: linear-gradient(145deg, rgba(0, 231, 182, 0.15) 0%, rgba(0, 200, 150, 0.08) 100%);
  border: 1px solid rgba(0, 231, 182, 0.3);
  color: #00e7b6;
}

.executor-buy-button:hover {
  background: linear-gradient(145deg, rgba(0, 231, 182, 0.25) 0%, rgba(0, 200, 150, 0.15) 100%);
  border-color: rgba(0, 231, 182, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 231, 182, 0.2);
}

.executor-sell-button {
  background: linear-gradient(145deg, rgba(255, 71, 87, 0.15) 0%, rgba(255, 50, 70, 0.08) 100%);
  border: 1px solid rgba(255, 71, 87, 0.3);
  color: #ff4757;
}

.executor-sell-button:hover {
  background: linear-gradient(145deg, rgba(255, 71, 87, 0.25) 0%, rgba(255, 50, 70, 0.15) 100%);
  border-color: rgba(255, 71, 87, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(255, 71, 87, 0.2);
}

.executor-button-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.executor-button-label {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;
  text-transform: uppercase;
}

.executor-button-price {
  font-size: 16px;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.9);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.01em;
}

/* Limit Order Buttons */
.executor-limit-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.executor-limit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 14px 20px;
  background: rgba(20, 20, 20, 0.6);
  backdrop-filter: blur(20px) saturate(150%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: rgba(255, 255, 255, 0.85);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 14px;
  font-weight: 600;
  width: 100%;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.executor-limit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.executor-limit-button:hover::before {
  left: 100%;
}

.executor-limit-button:hover {
  background: rgba(25, 25, 25, 0.8);
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.95);
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.executor-limit-text {
  font-size: 12px;
  font-weight: 600;
  color: #ffffff;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.01em;
}

/* Action Buttons */
.executor-action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.executor-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 18px;
  background: rgba(15, 15, 15, 0.7);
  backdrop-filter: blur(15px) saturate(120%);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  color: rgba(255, 255, 255, 0.75);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 13px;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.executor-action-button:hover {
  background: rgba(20, 20, 20, 0.85);
  border-color: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.executor-action-text {
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.01em;
}

/* Market Info */
.executor-market-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.executor-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(145deg, rgba(20, 20, 20, 0.6) 0%, rgba(15, 15, 15, 0.8) 100%);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 6px;
}

.executor-info-label {
  font-size: 13px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.6);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.01em;
  text-transform: uppercase;
}

.executor-info-value {
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.01em;
}

.executor-info-value.positive {
  color: #00e7b6;
}

.executor-info-value.negative {
  color: #ff4757;
}

.drawing-tools-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  background: linear-gradient(180deg, rgba(15, 15, 15, 0.98) 0%, rgba(10, 10, 10, 0.95) 100%);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  z-index: 1000;
  min-width: 280px; /* Increased width for better toggle spacing */
  max-width: 320px;
  animation: dropdownSlideIn 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-header {
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(15, 15, 15, 0.95);
}

.dropdown-header span {
  font-size: 12px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.01em;
  text-transform: uppercase;
}

/* Special styling for delete and clear all buttons */
.dropdown-tool-button.delete-button {
  color: rgba(255, 71, 87, 0.8);
}

.dropdown-tool-button.delete-button:hover {
  background: rgba(255, 71, 87, 0.1);
  color: #ff4757;
}

.dropdown-tool-button.clear-button {
  color: rgba(255, 165, 0, 0.8);
}

.dropdown-tool-button.clear-button:hover {
  background: rgba(255, 165, 0, 0.1);
  color: #ffa500;
}

/* Icon styling for delete and clear buttons */
.dropdown-tool-button.delete-button .dropdown-tool-icon-text {
  font-size: 14px;
  font-weight: 600;
}

.dropdown-tool-button.clear-button .dropdown-tool-icon-text {
  font-size: 14px;
  font-weight: 600;
}

/* Auto-cursor toggle styling */
.cursor-auto-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 18px; /* Increased padding for better spacing */
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: linear-gradient(145deg, rgba(15, 15, 15, 0.9) 0%, rgba(10, 10, 10, 0.95) 100%);
  gap: 16px; /* Add gap between label and toggle */
}

.toggle-label {
  display: flex;
  flex-direction: column;
  gap: 3px; /* Slightly increased gap */
  flex: 1; /* Take available space */
  min-width: 0; /* Allow text to wrap if needed */
}

.toggle-label span:first-child {
  font-size: 13px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.01em;
  line-height: 1.2;
}

.toggle-description {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.01em;
  line-height: 1.3;
  max-width: 180px;
}

/* Toggle switch styling */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 42px; /* Slightly wider for better proportions */
  height: 22px; /* Slightly taller */
  cursor: pointer;
  flex-shrink: 0; /* Prevent shrinking */
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  transition: all 0.2s ease;
  backdrop-filter: blur(5px);
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px; /* Increased to match new toggle size */
  width: 16px;
  left: 2px;
  top: 2px;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(200, 200, 200, 0.8) 100%);
  border-radius: 50%;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.toggle-switch input:checked + .toggle-slider {
  background: linear-gradient(145deg, rgba(0, 231, 182, 0.8) 0%, rgba(0, 200, 160, 0.9) 100%);
  border-color: rgba(0, 231, 182, 0.4);
  box-shadow: 0 0 8px rgba(0, 231, 182, 0.3);
}

.toggle-switch input:checked + .toggle-slider:before {
  transform: translateX(18px); /* Adjusted for new toggle width (42px - 16px - 4px padding) */
  background: linear-gradient(145deg, rgba(255, 255, 255, 1) 0%, rgba(240, 240, 240, 0.95) 100%);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
}

.dropdown-tools {
  padding: 12px 16px; /* Increased padding for better spacing */
  display: flex;
  flex-direction: column;
  gap: 4px; /* Slightly increased gap between tools */
}

.close-button {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 16px;
  cursor: pointer;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

/* Dropdown Tool Buttons */
.dropdown-tool-button {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: transparent;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  width: 100%;
  text-align: left;
}

.dropdown-tool-button:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
}

.dropdown-tool-button.active {
  background: rgba(0, 231, 182, 0.15);
  color: #00e7b6;
  border: 1px solid rgba(0, 231, 182, 0.3);
}

.dropdown-tool-icon-image {
  width: 16px;
  height: 16px;
  filter: brightness(0) saturate(100%) invert(70%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-tool-button:hover .dropdown-tool-icon-image {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
}

.dropdown-tool-button.active .dropdown-tool-icon-image {
  filter: brightness(0) saturate(100%) invert(85%) sepia(15%) saturate(1000%) hue-rotate(120deg) brightness(95%) contrast(95%);
}

.dropdown-tool-icon,
.dropdown-tool-icon-text {
  color: inherit;
  font-size: 14px;
  font-weight: 600;
}

.dropdown-tool-name {
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.01em;
  flex: 1;
  text-align: left;
}

.drawing-tools-content {
  flex: 1;
  padding: 8px 4px;
  overflow: hidden;
}

.tool-category {
  margin-bottom: 16px;
}

.category-header {
  font-size: 8px;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 600;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  padding: 0 4px;
  text-align: center;
}

.tool-grid {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  padding: 8px 0;
}

.tool-button {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, rgba(30, 30, 30, 0.85) 0%, rgba(20, 20, 20, 0.7) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.08),
    0 1px 4px rgba(0, 0, 0, 0.6),
    0 0 0 0 rgba(255, 255, 255, 0);
  backdrop-filter: blur(12px);
}

.tool-button:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);
  border-color: rgba(255, 255, 255, 0.25);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.7),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.tool-button.active {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.12),
    0 2px 10px rgba(0, 0, 0, 0.8),
    0 0 0 1px rgba(255, 255, 255, 0.3);
}

.tool-icon {
  color: rgba(255, 255, 255, 0.7);
  font-size: 18px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.1));
}

.tool-icon-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
  font-weight: 600;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.1));
  text-shadow: 0 0 3px rgba(255, 255, 255, 0.2);
}

.tool-icon-image {
  width: 20px;
  height: 20px;
  filter: brightness(0) saturate(100%) invert(70%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%) drop-shadow(0 0 2px rgba(255, 255, 255, 0.1));
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.tool-button:hover .tool-icon,
.tool-button:hover .tool-icon-text {
  color: #ffffff;
  filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.3));
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.tool-button:hover .tool-icon-image {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%) drop-shadow(0 0 6px rgba(255, 255, 255, 0.3));
  transform: scale(1.1);
}

.tool-button.active .tool-icon,
.tool-button.active .tool-icon-text {
  color: #ffffff;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
  text-shadow: 0 0 12px rgba(255, 255, 255, 0.3);
  transform: scale(1.15);
}

.tool-button.active .tool-icon-image {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%) drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
  transform: scale(1.15);
}

/* Tooltip for tool buttons */
.tool-button::after {
  content: attr(title);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(20, 20, 20, 0.95);
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  margin-left: 8px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  z-index: 1000;
  backdrop-filter: blur(12px);
}

.tool-button:hover::after {
  opacity: 1;
}

/* Adjust chart area when sidebar is open */
.chart-content .chart-area {
  flex: 1;
}

/* Drawing mode cursor - improved */
.chart-container.drawing-mode {
  cursor: crosshair !important;
  position: relative;
}

.chart-container.drawing-mode * {
  cursor: crosshair !important;
}

/* Improved drawing mode overlay for better interaction */
.chart-container.drawing-mode::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  pointer-events: none;
  border: 2px solid rgba(0, 231, 182, 0.3);
  border-radius: 4px;
  animation: drawing-mode-pulse 2s infinite;
}

@keyframes drawing-mode-pulse {
  0%, 100% {
    border-color: rgba(0, 231, 182, 0.2);
    box-shadow: inset 0 0 20px rgba(0, 231, 182, 0.1);
  }
  50% {
    border-color: rgba(0, 231, 182, 0.4);
    box-shadow: inset 0 0 30px rgba(0, 231, 182, 0.2);
  }
}

.drawing-instruction {
  color: rgba(0, 231, 182, 0.8);
  font-style: italic;
  font-size: 11px;
}

/* Drawing counter badge */
.drawings-counter {
  background-color: #00e7b6;
  color: #000000;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 9px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 8px rgba(0, 231, 182, 0.5);
  animation: pulse-badge 2s infinite;
  margin-top: 2px;
}

.drawing-count {
  position: absolute;
  top: -6px;
  right: -6px;
  background-color: #00e7b6;
  color: #000000;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 10px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 8px rgba(0, 231, 182, 0.5);
  animation: pulse-badge 2s infinite;
}

@keyframes pulse-badge {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 8px rgba(0, 231, 182, 0.5);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 12px rgba(0, 231, 182, 0.8);
  }
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .drawing-tools-sidebar {
    width: 50px;
  }

  .tool-button {
    width: 42px;
    height: 42px;
  }

  .tool-icon,
  .tool-icon-text {
    font-size: 14px;
  }

  .tool-icon-image {
    width: 14px;
    height: 14px;
  }

  .tool-button::after {
    display: none; /* Hide tooltips on mobile */
  }
}

/* Compact mode for smaller screens */
@media (max-width: 480px) {
  .drawing-tools-sidebar {
    width: 44px;
  }

  .tool-button {
    width: 36px;
    height: 36px;
  }

  .tool-icon,
  .tool-icon-text {
    font-size: 12px;
  }

  .tool-icon-image {
    width: 12px;
    height: 12px;
  }

  .drawing-tools-content {
    padding: 4px 2px;
  }

  .tool-category {
    margin-bottom: 8px;
  }
}

.chart-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #0A0A0A;
}

.chart-header {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  font-size: 13px;
  background: linear-gradient(180deg, rgba(15, 15, 15, 0.95) 0%, rgba(10, 10, 10, 0.98) 100%);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 5;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Drawing Status Indicator */
.drawing-status {
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: rgba(0, 231, 182, 0.1);
  border: 1px solid rgba(0, 231, 182, 0.3);
  border-radius: 6px;
  padding: 6px 12px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 0 8px rgba(0, 231, 182, 0.3);
  }
  50% {
    box-shadow: 0 0 16px rgba(0, 231, 182, 0.5);
  }
}

.drawing-status-text {
  font-size: 12px;
  font-weight: 600;
  color: #00e7b6;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.01em;
}

.cancel-drawing {
  background: rgba(255, 82, 82, 0.2);
  border: 1px solid rgba(255, 82, 82, 0.4);
  color: #ff5252;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.cancel-drawing:hover {
  background: rgba(255, 82, 82, 0.3);
  border-color: rgba(255, 82, 82, 0.6);
  box-shadow: 0 0 8px rgba(255, 82, 82, 0.3);
}

/* Keyboard Shortcuts Panel */
.shortcuts-panel {
  position: absolute;
  left: 100%;
  top: 0;
  width: 200px;
  background-color: rgba(0, 0, 0, 0.95);
  border: 1px solid rgba(0, 231, 182, 0.2);
  border-radius: 8px;
  padding: 12px;
  margin-left: 8px;
  z-index: 1000;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
}

.shortcuts-header {
  color: #00e7b6;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid rgba(0, 231, 182, 0.2);
  padding-bottom: 6px;
}

.shortcuts-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.shortcut-key {
  background-color: rgba(0, 231, 182, 0.1);
  border: 1px solid rgba(0, 231, 182, 0.3);
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 10px;
  font-weight: 600;
  color: #00e7b6;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  min-width: 45px;
  text-align: center;
}

.shortcut-desc {
  color: rgba(255, 255, 255, 0.8);
  font-size: 11px;
  flex: 1;
  text-align: right;
}

/* Hide shortcuts panel on mobile */
@media (max-width: 768px) {
  .shortcuts-panel {
    display: none;
  }
}

.symbol-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.symbol {
  font-size: 15px;
  font-weight: 600;
  color: #ffffff;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
}

.price-info {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-right: 30px;
  position: relative;
}

.price-info::after {
  content: '';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  height: 20px;
  width: 1px;
  background: linear-gradient(to bottom, rgba(0, 231, 182, 0.01), rgba(0, 231, 182, 0.2), rgba(0, 231, 182, 0.01));
}

.price-label {
  color: #787b86;
  font-size: 14px;
  font-weight: 500;
  margin-right: 5px;
}

.price-value {
  color: #d1d4dc;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;
  font-weight: 500;
  font-size: 14px;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.1);
}

.volume-info {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(0, 0, 0, 0.3);
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid rgba(0, 231, 182, 0.1);
  margin-left: 8px;
}

.volume-label {
  font-size: 10px;
  font-weight: 500;
  color: #787b86;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.01em;
  margin-right: 2px;
}

.volume-value {
  font-size: 10px;
  font-weight: 500;
  color: #d1d4dc;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.01em;
}

.volume-change {
  font-size: 9px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.01em;
  padding: 1px 4px;
  border-radius: 2px;
  margin-left: 2px;
}

.volume-change.positive {
  color: #00e7b6;
  background-color: rgba(0, 231, 182, 0.1);
  border: 1px solid rgba(0, 231, 182, 0.2);
  text-shadow: 0 0 5px rgba(0, 231, 182, 0.3);
}

.volume-change.negative {
  color: #ffffff;
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

.chart-container {
  flex: 1;
  height: 100%;
  min-height: 400px;
  background-color: #0A0A0A;
  position: relative;
}



/* Hide TradingView watermark */
.chart-container .tv-lightweight-charts > div > div > div:last-child,
.chart-container div[id^="tradingview_"] > div > div > div:last-child,
.chart-container canvas + div,
.chart-container div[class*="watermark"],
.chart-container div[class*="attribution"] {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
  pointer-events: none !important;
}





/* Style chart axis text */
.chart-container text {
  font-family: 'Roboto Mono', monospace !important;
  font-size: 10px !important;
  font-weight: 500 !important;
  fill: rgba(0, 231, 182, 0.7) !important;
  text-shadow: 0 0 5px rgba(0, 231, 182, 0.3);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .future-header h1 {
    font-size: 28px;
  }

  .chart-toolbar {
    flex-wrap: wrap;
    height: auto;
    padding: 15px;
    gap: 15px;
  }

  .toolbar-left {
    width: 100%;
    order: 1;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .chart-search-bar {
    width: 60%;
  }

  .timeframe-selector {
    width: auto;
  }

  .chart-controls {
    position: absolute;
    top: 60px; /* Below toolbar on mobile */
    right: 16px;
    width: auto;
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .future-header h1 {
    font-size: 24px;
  }

  .toolbar-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .chart-search-bar {
    width: 100%;
  }

  .timeframe-selector {
    width: 100%;
    justify-content: flex-start;
  }

  .chart-button span,
  .filters-button span {
    display: none;
  }

  .chart-header {
    flex-direction: column;
    gap: 15px;
    padding: 15px;
  }

  .symbol-info, .price-info, .volume-info {
    margin-right: 0;
    width: 100%;
  }

  .price-info {
    justify-content: space-between;
  }

  .price-info::after {
    display: none;
  }

  .side-toolbar {
    padding: 8px 4px;
  }

  .side-button {
    width: 32px;
    height: 32px;
    margin-bottom: 12px;
  }

  .chart-container {
    min-height: 350px;
  }
}

@media (max-width: 480px) {
  .future-header h1 {
    font-size: 20px;
  }

  .timeframe-selector {
    justify-content: space-between;
    width: 100%;
  }

  .chart-controls {
    position: absolute;
    top: 80px; /* Further down on smaller screens */
    right: 8px;
    justify-content: center;
    padding: 6px 8px; /* Smaller padding on mobile */
    gap: 6px;
  }

  .price-info {
    flex-wrap: wrap;
    gap: 10px;
  }

  .price-value {
    margin-right: 5px;
  }

  .chart-container {
    min-height: 300px;
  }
}

/* Delete Panel Styles */
.delete-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.95);
  border: 1px solid rgba(0, 231, 182, 0.3);
  border-radius: 12px;
  padding: 20px;
  min-width: 300px;
  max-width: 500px;
  max-height: 400px;
  z-index: 1000;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.8),
    0 0 30px rgba(0, 231, 182, 0.2);
  backdrop-filter: blur(20px);
}

.delete-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(0, 231, 182, 0.2);
}

.delete-panel-header h3 {
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.close-delete-panel {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-delete-panel:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ff6b6b;
}

.drawings-list {
  max-height: 300px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.drawing-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: rgba(0, 231, 182, 0.05);
  border: 1px solid rgba(0, 231, 182, 0.1);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.drawing-item:hover {
  background: rgba(0, 231, 182, 0.1);
  border-color: rgba(0, 231, 182, 0.2);
}

.drawing-info {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.delete-drawing-btn {
  background: #ff4444;
  border: none;
  color: #ffffff;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.delete-drawing-btn:hover {
  background: #ff6666;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 68, 68, 0.3);
}

/* Star button styling */
.star-button {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 6px;
  padding: 4px 8px;
  margin-left: 8px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.star-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.25);
  color: rgba(255, 255, 255, 0.8);
  transform: scale(1.05);
}

.star-button.starred {
  background: rgba(255, 193, 7, 0.2);
  border-color: rgba(255, 193, 7, 0.4);
  color: #ffc107;
}

.star-button.starred:hover {
  background: rgba(255, 193, 7, 0.3);
  border-color: rgba(255, 193, 7, 0.5);
}

/* Buy/Sell Buttons - OSIS Style */
.buy-sell-buttons {
  position: absolute;
  top: 60px;
  left: 16px;
  display: flex;
  gap: 6px;
  z-index: 10;
}

.buy-button,
.sell-button {
  display: flex;
  flex-direction: column-reverse;
  align-items: center;
  justify-content: center;
  min-width: 85px;
  height: 36px;
  border: 1px solid;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 600;
  position: relative;
  backdrop-filter: blur(12px);
  overflow: hidden;
}

.buy-button {
  border-color: rgba(0, 231, 182, 0.5);
  color: #ffffff;
  background: linear-gradient(135deg,
    rgba(0, 231, 182, 0.9) 0%,
    rgba(0, 200, 160, 0.95) 50%,
    rgba(0, 180, 140, 1) 100%
  );
  box-shadow:
    0 3px 12px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(0, 231, 182, 0.3),
    0 0 25px rgba(0, 231, 182, 0.4);
}

.buy-button:hover {
  border-color: rgba(0, 231, 182, 0.8);
  transform: translateY(-2px);
  background: linear-gradient(135deg,
    rgba(0, 231, 182, 1) 0%,
    rgba(0, 210, 170, 1) 50%,
    rgba(0, 190, 150, 1) 100%
  );
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(0, 231, 182, 0.5),
    0 0 35px rgba(0, 231, 182, 0.6),
    0 0 50px rgba(0, 231, 182, 0.3);
}

.sell-button {
  border-color: rgba(255, 71, 87, 0.5);
  color: #ffffff;
  background: linear-gradient(135deg,
    rgba(255, 71, 87, 0.9) 0%,
    rgba(240, 60, 75, 0.95) 50%,
    rgba(220, 50, 65, 1) 100%
  );
  box-shadow:
    0 3px 12px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 71, 87, 0.3),
    0 0 25px rgba(255, 71, 87, 0.4);
}

.sell-button:hover {
  border-color: rgba(255, 71, 87, 0.8);
  transform: translateY(-2px);
  background: linear-gradient(135deg,
    rgba(255, 71, 87, 1) 0%,
    rgba(245, 65, 80, 1) 50%,
    rgba(230, 55, 70, 1) 100%
  );
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 71, 87, 0.5),
    0 0 35px rgba(255, 71, 87, 0.6),
    0 0 50px rgba(255, 71, 87, 0.3);
}

.button-label {
  font-size: 11px;
  font-weight: 800;
  color: #ffffff;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;
  text-transform: uppercase;
  line-height: 1;
  margin-bottom: 2px;
  text-shadow:
    0 0 4px currentColor,
    0 1px 2px rgba(0, 0, 0, 0.8);
}

.button-price {
  font-size: 8px;
  font-weight: 600;
  color: #ffffff;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.01em;
  line-height: 1;
  margin-bottom: 0px;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}

.button-change {
  font-size: 7px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.01em;
  line-height: 1;
  opacity: 0.8;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}

/* Executor Trading Form Styles */
.executor-trading-form {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.executor-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.executor-form-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.executor-form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.executor-form-label {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.01em;
}

.executor-form-input,
.executor-form-select {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px 16px;
  color: #ffffff;
  font-size: 14px;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  transition: all 0.2s ease;
}

.executor-form-input:focus,
.executor-form-select:focus {
  outline: none;
  border-color: #00e7b6;
  box-shadow: 0 0 0 2px rgba(0, 231, 182, 0.2);
}

.executor-form-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.executor-form-select option {
  background: #1a1a1a;
  color: #ffffff;
}

.executor-form-summary {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  padding: 16px;
  margin-top: 8px;
}

.executor-summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: -0.01em;
}

.executor-summary-row span:first-child {
  color: rgba(255, 255, 255, 0.7);
}

.executor-summary-row span:last-child {
  color: #ffffff;
  font-weight: 600;
}

.executor-submit-button {
  background: linear-gradient(135deg, #00e7b6 0%, #00c4a0 100%);
  border: none;
  border-radius: 8px;
  padding: 14px 24px;
  color: #000000;
  font-size: 16px;
  font-weight: 600;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.01em;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  width: 100%;
  margin-top: 16px;
}

.executor-submit-button:hover {
  background: linear-gradient(135deg, #00f0c0 0%, #00d4b0 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 231, 182, 0.3);
}

.executor-submit-button.sell {
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
}

.executor-submit-button.sell:hover {
  background: linear-gradient(135deg, #ff5a6b 0%, #ff4757 100%);
  box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
}

.executor-submit-button:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.4);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Mobile responsive for trading form */
@media (max-width: 768px) {
  .executor-trading-form {
    padding: 16px;
  }
  
  .executor-form-header {
    margin-bottom: 16px;
    padding-bottom: 12px;
  }
  
  .executor-form-content {
    gap: 12px;
  }
  
  .executor-form-input,
  .executor-form-select {
    padding: 10px 14px;
    font-size: 13px;
  }
  
  .executor-submit-button {
    padding: 12px 20px;
    font-size: 15px;
  }
}

/* Icon styling for delete and clear buttons */
.dropdown-tool-button.delete-button .dropdown-tool-icon-text {
  font-size: 14px;
  font-weight: 600;
}

.dropdown-tool-button.clear-button .dropdown-tool-icon-text {
  font-size: 14px;
  font-weight: 600;
}

/* SVG icon styling for delete and clear buttons */
.dropdown-tool-button.delete-button .dropdown-tool-icon-text svg {
  width: 16px;
  height: 16px;
  stroke: currentColor;
  fill: none;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.dropdown-tool-button.clear-button .dropdown-tool-icon-text svg {
  width: 16px;
  height: 16px;
  stroke: currentColor;
  fill: none;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.dropdown-tool-button.delete-button:hover .dropdown-tool-icon-text svg {
  stroke: #ff4757;
}

.dropdown-tool-button.clear-button:hover .dropdown-tool-icon-text svg {
  stroke: #ffa500;
}

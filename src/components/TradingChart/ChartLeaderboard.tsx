import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Trophy, Crown, Medal, ChevronUp, ChevronDown, TrendingUp, TrendingDown } from 'lucide-react';
import { useCompetitions, LeaderboardEntry } from '@/hooks/useCompetitions';
import './ChartLeaderboard.css';

interface ChartLeaderboardProps {
  competitionId?: string;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  className?: string;
}

const ChartLeaderboard: React.FC<ChartLeaderboardProps> = ({
  competitionId = 'default-competition', // Default competition ID
  position = 'bottom-right',
  className = ''
}) => {
  const { getCompetitionDetails } = useCompetitions();
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const [loading, setLoading] = useState(true);

  // Mock data for demonstration if no real competition data
  const mockLeaderboard: LeaderboardEntry[] = [
    {
      id: '1',
      competition_id: 'mock',
      user_id: '1',
      participant_id: '1',
      username: 'Felix T.',
      current_rank: 1,
      portfolio_value: 125440,
      total_return: 25440,
      return_percent: 25.44,
      unrealized_pnl: 2340,
      realized_pnl: 23100,
      total_trades: 47,
      last_updated: new Date().toISOString()
    },
    {
      id: '2',
      competition_id: 'mock',
      user_id: '2',
      participant_id: '2',
      username: 'Paula E.',
      current_rank: 2,
      portfolio_value: 118200,
      total_return: 18200,
      return_percent: 18.20,
      unrealized_pnl: 1850,
      realized_pnl: 16350,
      total_trades: 32,
      last_updated: new Date().toISOString()
    },
    {
      id: '3',
      competition_id: 'mock',
      user_id: '3',
      participant_id: '3',
      username: 'Melinda H.',
      current_rank: 3,
      portfolio_value: 112800,
      total_return: 12800,
      return_percent: 12.80,
      unrealized_pnl: 980,
      realized_pnl: 11820,
      total_trades: 28,
      last_updated: new Date().toISOString()
    },
    {
      id: '4',
      competition_id: 'mock',
      user_id: '4',
      participant_id: '4',
      username: 'Kenneth H.',
      current_rank: 4,
      portfolio_value: 108500,
      total_return: 8500,
      return_percent: 8.50,
      unrealized_pnl: 650,
      realized_pnl: 7850,
      total_trades: 19,
      last_updated: new Date().toISOString()
    },
    {
      id: '5',
      competition_id: 'mock',
      user_id: '5',
      participant_id: '5',
      username: 'Sarah M.',
      current_rank: 5,
      portfolio_value: 105200,
      total_return: 5200,
      return_percent: 5.20,
      unrealized_pnl: 420,
      realized_pnl: 4780,
      total_trades: 15,
      last_updated: new Date().toISOString()
    }
  ];

  const fetchLeaderboard = async () => {
    try {
      const details = await getCompetitionDetails(competitionId);
      if (details && details.leaderboard && details.leaderboard.length > 0) {
        setLeaderboard(details.leaderboard.slice(0, 5)); // Top 5
      } else {
        // Use mock data if no real data available
        setLeaderboard(mockLeaderboard);
      }
    } catch (error) {
      console.error('Error fetching leaderboard:', error);
      // Fallback to mock data
      setLeaderboard(mockLeaderboard);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLeaderboard();
    const interval = setInterval(fetchLeaderboard, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [competitionId]);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-4 h-4 text-yellow-400" />;
      case 2:
        return <Medal className="w-4 h-4 text-gray-300" />;
      case 3:
        return <Medal className="w-4 h-4 text-amber-600" />;
      default:
        return <span className="w-4 h-4 flex items-center justify-center text-xs font-bold text-white/60">#{rank}</span>;
    }
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'top-right':
        return 'top-20 right-4';
      case 'top-left':
        return 'top-20 left-4';
      default:
        return 'bottom-4 right-4';
    }
  };

  if (loading) {
    return (
      <div className={`chart-leaderboard-container ${getPositionClasses()} ${className}`}>
        <div className="chart-leaderboard-compact">
          <div className="leaderboard-loading">
            <div className="loading-spinner"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`chart-leaderboard-container ${getPositionClasses()} ${className}`}>
      <AnimatePresence mode="wait">
        {!isExpanded ? (
          <motion.div
            key="compact"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="chart-leaderboard-compact"
            onClick={() => setIsExpanded(true)}
          >
            <div className="compact-header">
              <Trophy className="w-4 h-4 text-yellow-400" />
              <span className="compact-title">Leaderboard</span>
              <ChevronUp className="w-3 h-3 text-white/60" />
            </div>
            {leaderboard.length > 0 && (
              <div className="compact-leader">
                <div className="leader-rank">
                  {getRankIcon(1)}
                </div>
                <div className="leader-info">
                  <div className="leader-name">{leaderboard[0].username}</div>
                  <div className="leader-return">
                    {formatPercentage(leaderboard[0].return_percent)}
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        ) : (
          <motion.div
            key="expanded"
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="chart-leaderboard-expanded"
          >
            <div className="expanded-header">
              <div className="header-left">
                <Trophy className="w-5 h-5 text-yellow-400" />
                <span className="expanded-title">Top Traders</span>
              </div>
              <button
                className="collapse-button"
                onClick={() => setIsExpanded(false)}
              >
                <ChevronDown className="w-4 h-4" />
              </button>
            </div>
            
            <div className="leaderboard-list">
              {leaderboard.map((entry, index) => (
                <motion.div
                  key={entry.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="leaderboard-entry"
                >
                  <div className="entry-rank">
                    {getRankIcon(entry.current_rank)}
                  </div>
                  <div className="entry-info">
                    <div className="entry-name">{entry.username}</div>
                    <div className="entry-value">{formatCurrency(entry.portfolio_value)}</div>
                  </div>
                  <div className="entry-return">
                    <div className={`return-value ${entry.return_percent >= 0 ? 'positive' : 'negative'}`}>
                      {formatPercentage(entry.return_percent)}
                    </div>
                    <div className="return-icon">
                      {entry.return_percent >= 0 ? (
                        <TrendingUp className="w-3 h-3" />
                      ) : (
                        <TrendingDown className="w-3 h-3" />
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ChartLeaderboard;

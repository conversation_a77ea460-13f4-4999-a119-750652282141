import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Trophy, TrendingUp, TrendingDown, ChevronUp, ChevronDown } from 'lucide-react';
import { useCompetitions, LeaderboardEntry } from '@/hooks/useCompetitions';

// Extend LeaderboardEntry to include win_rate for display
interface ExtendedLeaderboardEntry extends LeaderboardEntry {
  win_rate: number;
}

interface ChartLeaderboardProps {
  competitionId?: string;
  className?: string;
}

const ChartLeaderboard: React.FC<ChartLeaderboardProps> = ({ 
  competitionId = 'default-competition',
  className = ''
}) => {
  const { getCompetitionDetails } = useCompetitions();
  const [isExpanded, setIsExpanded] = useState(false);
  const [leaderboard, setLeaderboard] = useState<ExtendedLeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);

  // Mock data matching the UI style shown
  const mockLeaderboard: ExtendedLeaderboardEntry[] = [
    {
      id: '1',
      competition_id: 'comp1',
      user_id: 'user1',
      participant_id: 'part1',
      username: '<PERSON>',
      current_rank: 1,
      portfolio_value: 125440,
      total_return: 25.44,
      return_percent: 25.44,
      unrealized_pnl: 2544,
      realized_pnl: 23000,
      total_trades: 12,
      win_rate: 83.3,
      last_updated: new Date().toISOString()
    },
    {
      id: '2',
      competition_id: 'comp1',
      user_id: 'user2',
      participant_id: 'part2',
      username: 'Paula E.',
      current_rank: 2,
      portfolio_value: 118200,
      total_return: 18.20,
      return_percent: 18.20,
      unrealized_pnl: 1820,
      realized_pnl: 16380,
      total_trades: 8,
      win_rate: 75.0,
      last_updated: new Date().toISOString()
    },
    {
      id: '3',
      competition_id: 'comp1',
      user_id: 'user3',
      participant_id: 'part3',
      username: 'Melinda H.',
      current_rank: 3,
      portfolio_value: 112800,
      total_return: 12.80,
      return_percent: 12.80,
      unrealized_pnl: 1280,
      realized_pnl: 11520,
      total_trades: 15,
      win_rate: 66.7,
      last_updated: new Date().toISOString()
    },
    {
      id: '4',
      competition_id: 'comp1',
      user_id: 'user4',
      participant_id: 'part4',
      username: 'Kenneth H.',
      current_rank: 4,
      portfolio_value: 108500,
      total_return: 8.50,
      return_percent: 8.50,
      unrealized_pnl: 850,
      realized_pnl: 7650,
      total_trades: 6,
      win_rate: 83.3,
      last_updated: new Date().toISOString()
    },
    {
      id: '5',
      competition_id: 'comp1',
      user_id: 'user5',
      participant_id: 'part5',
      username: 'Sarah M.',
      current_rank: 5,
      portfolio_value: 105200,
      total_return: 5.20,
      return_percent: 5.20,
      unrealized_pnl: 520,
      realized_pnl: 4680,
      total_trades: 10,
      win_rate: 60.0,
      last_updated: new Date().toISOString()
    }
  ];

  useEffect(() => {
    const fetchLeaderboard = async () => {
      try {
        // Try to fetch real data, fall back to mock data
        const details = await getCompetitionDetails(competitionId);
        if (details && details.leaderboard && details.leaderboard.length > 0) {
          setLeaderboard(details.leaderboard.slice(0, 5)); // Top 5
        } else {
          setLeaderboard(mockLeaderboard);
        }
      } catch (error) {
        console.error('Error fetching leaderboard:', error);
        setLeaderboard(mockLeaderboard);
      } finally {
        setLoading(false);
      }
    };

    fetchLeaderboard();
  }, [competitionId, getCompetitionDetails]);

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <div className="w-5 h-5 rounded-sm bg-yellow-500 flex items-center justify-center text-xs font-bold text-black">👑</div>;
      case 2:
        return <div className="w-5 h-5 rounded-sm bg-gray-600 flex items-center justify-center text-xs font-bold text-white">🥈</div>;
      case 3:
        return <div className="w-5 h-5 rounded-sm bg-amber-600 flex items-center justify-center text-xs font-bold text-white">🥉</div>;
      case 4:
        return <div className="w-5 h-5 rounded-sm bg-gray-700 flex items-center justify-center text-xs font-bold text-white">#4</div>;
      case 5:
        return <div className="w-5 h-5 rounded-sm bg-gray-700 flex items-center justify-center text-xs font-bold text-white">#5</div>;
      default:
        return <div className="w-5 h-5 rounded-sm bg-gray-700 flex items-center justify-center text-xs font-bold text-white">#{rank}</div>;
    }
  };

  const getReturnIcon = (returnValue: number) => {
    return returnValue >= 0 ? 
      <TrendingUp className="w-3 h-3 text-green-400" /> : 
      <TrendingDown className="w-3 h-3 text-red-400" />;
  };

  const getReturnColor = (returnValue: number) => {
    return returnValue >= 0 ? 'text-green-400' : 'text-red-400';
  };

  if (loading) {
    return (
      <div className={`fixed bottom-4 right-4 z-40 ${className}`}>
        <div className="bg-[#141414]/90 backdrop-blur-md border border-white/10 rounded-xl p-3 shadow-2xl">
          <div className="animate-pulse">
            <div className="h-4 bg-white/20 rounded w-20 mb-2"></div>
            <div className="h-3 bg-white/10 rounded w-16"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`fixed bottom-4 right-4 z-40 ${className}`}>
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-[#1a1a1a] border border-gray-700 rounded-lg shadow-2xl overflow-hidden min-w-[280px]"
        style={{
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.6)'
        }}
      >
        {/* Header - Always Visible */}
        <div
          className="px-3 py-2 bg-[#2a2a2a] cursor-pointer hover:bg-[#2f2f2f] transition-colors border-b border-gray-700"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Trophy className="w-4 h-4 text-yellow-400" />
              <span className="text-white text-sm font-medium">Top Traders</span>
            </div>
            <motion.div
              animate={{ rotate: isExpanded ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronDown className="w-4 h-4 text-gray-400" />
            </motion.div>
          </div>
          
          {/* Top 3 Preview when collapsed */}
          {!isExpanded && leaderboard.length > 0 && (
            <div className="mt-2 space-y-1">
              {leaderboard.slice(0, 3).map((entry) => (
                <div key={entry.id} className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    {getRankIcon(entry.current_rank)}
                    <span className="text-white/80">{entry.username}</span>
                  </div>
                  <div className={`flex items-center gap-1 ${getReturnColor(entry.total_return)}`}>
                    <span className="font-medium">+{entry.total_return.toFixed(2)}%</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Expanded Content */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
              className="bg-[#1a1a1a]"
            >
              <div className="space-y-0 max-h-64 overflow-y-auto">
                {leaderboard.map((entry, index) => (
                  <motion.div
                    key={entry.id}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="flex items-center justify-between px-3 py-2 hover:bg-[#2a2a2a] transition-colors border-b border-gray-800 last:border-b-0"
                  >
                    <div className="flex items-center gap-3">
                      {getRankIcon(entry.current_rank)}
                      <div>
                        <div className="text-white text-sm font-medium">{entry.username}</div>
                        <div className="text-gray-400 text-xs">
                          ${(entry.portfolio_value || 100000).toLocaleString()}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`${getReturnColor(entry.total_return)}`}>
                        <span className="font-bold text-sm">+{entry.total_return.toFixed(2)}%</span>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
};

export default ChartLeaderboard;

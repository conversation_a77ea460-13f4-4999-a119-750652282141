import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Trophy, TrendingUp, TrendingDown, ChevronUp, ChevronDown } from 'lucide-react';
import { useCompetitions, LeaderboardEntry } from '@/hooks/useCompetitions';

interface ChartLeaderboardProps {
  competitionId?: string;
  className?: string;
}

const ChartLeaderboard: React.FC<ChartLeaderboardProps> = ({ 
  competitionId = 'default-competition',
  className = ''
}) => {
  const { getCompetitionDetails } = useCompetitions();
  const [isExpanded, setIsExpanded] = useState(false);
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);

  // Mock data for demonstration
  const mockLeaderboard: LeaderboardEntry[] = [
    {
      id: '1',
      user_id: 'user1',
      username: '<PERSON>',
      current_rank: 1,
      total_return: 24.5,
      total_trades: 12,
      win_rate: 83.3,
      avatar_url: null,
      last_updated: new Date().toISOString()
    },
    {
      id: '2',
      user_id: 'user2',
      username: '<PERSON>.',
      current_rank: 2,
      total_return: 18.2,
      total_trades: 8,
      win_rate: 75.0,
      avatar_url: null,
      last_updated: new Date().toISOString()
    },
    {
      id: '3',
      user_id: 'user3',
      username: '<PERSON> <PERSON>.',
      current_rank: 3,
      total_return: 15.7,
      total_trades: 15,
      win_rate: 66.7,
      avatar_url: null,
      last_updated: new Date().toISOString()
    },
    {
      id: '4',
      user_id: 'user4',
      username: 'Emma L.',
      current_rank: 4,
      total_return: 12.3,
      total_trades: 6,
      win_rate: 83.3,
      avatar_url: null,
      last_updated: new Date().toISOString()
    },
    {
      id: '5',
      user_id: 'user5',
      username: 'David C.',
      current_rank: 5,
      total_return: 9.8,
      total_trades: 10,
      win_rate: 60.0,
      avatar_url: null,
      last_updated: new Date().toISOString()
    }
  ];

  useEffect(() => {
    const fetchLeaderboard = async () => {
      try {
        // Try to fetch real data, fall back to mock data
        const details = await getCompetitionDetails(competitionId);
        if (details && details.leaderboard && details.leaderboard.length > 0) {
          setLeaderboard(details.leaderboard.slice(0, 5)); // Top 5
        } else {
          setLeaderboard(mockLeaderboard);
        }
      } catch (error) {
        console.error('Error fetching leaderboard:', error);
        setLeaderboard(mockLeaderboard);
      } finally {
        setLoading(false);
      }
    };

    fetchLeaderboard();
  }, [competitionId, getCompetitionDetails]);

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Trophy className="w-4 h-4 text-yellow-400" />;
      case 2:
        return <div className="w-4 h-4 rounded-full bg-gray-400 flex items-center justify-center text-xs font-bold text-black">2</div>;
      case 3:
        return <div className="w-4 h-4 rounded-full bg-amber-600 flex items-center justify-center text-xs font-bold text-white">3</div>;
      default:
        return <div className="w-4 h-4 rounded-full bg-white/20 flex items-center justify-center text-xs font-bold text-white">{rank}</div>;
    }
  };

  const getReturnIcon = (returnValue: number) => {
    return returnValue >= 0 ? 
      <TrendingUp className="w-3 h-3 text-green-400" /> : 
      <TrendingDown className="w-3 h-3 text-red-400" />;
  };

  const getReturnColor = (returnValue: number) => {
    return returnValue >= 0 ? 'text-green-400' : 'text-red-400';
  };

  if (loading) {
    return (
      <div className={`fixed bottom-4 right-4 z-40 ${className}`}>
        <div className="bg-[#141414]/90 backdrop-blur-md border border-white/10 rounded-xl p-3 shadow-2xl">
          <div className="animate-pulse">
            <div className="h-4 bg-white/20 rounded w-20 mb-2"></div>
            <div className="h-3 bg-white/10 rounded w-16"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`fixed bottom-4 right-4 z-40 ${className}`}>
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-[#141414]/90 backdrop-blur-md border border-white/10 rounded-xl shadow-2xl overflow-hidden"
        style={{
          boxShadow: 'inset 0 1px 0 0 rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.4)'
        }}
      >
        {/* Header - Always Visible */}
        <div 
          className="p-3 cursor-pointer hover:bg-white/5 transition-colors"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Trophy className="w-4 h-4 text-yellow-400" />
              <span className="text-white text-sm font-medium">Leaderboard</span>
            </div>
            <motion.div
              animate={{ rotate: isExpanded ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronDown className="w-4 h-4 text-white/60" />
            </motion.div>
          </div>
          
          {/* Top 3 Preview when collapsed */}
          {!isExpanded && leaderboard.length > 0 && (
            <div className="mt-2 space-y-1">
              {leaderboard.slice(0, 3).map((entry) => (
                <div key={entry.id} className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    {getRankIcon(entry.current_rank)}
                    <span className="text-white/80">{entry.username}</span>
                  </div>
                  <div className={`flex items-center gap-1 ${getReturnColor(entry.total_return)}`}>
                    {getReturnIcon(entry.total_return)}
                    <span className="font-medium">{entry.total_return.toFixed(1)}%</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Expanded Content */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
              className="border-t border-white/10"
            >
              <div className="p-3 space-y-2 max-h-64 overflow-y-auto">
                {leaderboard.map((entry, index) => (
                  <motion.div
                    key={entry.id}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="flex items-center justify-between p-2 rounded-lg hover:bg-white/5 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      {getRankIcon(entry.current_rank)}
                      <div>
                        <div className="text-white text-sm font-medium">{entry.username}</div>
                        <div className="text-white/50 text-xs">
                          {entry.total_trades} trades • {entry.win_rate.toFixed(0)}% win rate
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`flex items-center gap-1 ${getReturnColor(entry.total_return)}`}>
                        {getReturnIcon(entry.total_return)}
                        <span className="font-bold text-sm">{entry.total_return.toFixed(1)}%</span>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
};

export default ChartLeaderboard;
